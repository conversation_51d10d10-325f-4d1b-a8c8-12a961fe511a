#!/usr/bin/env node

/**
 * CCALC Mobile App - Setup Verification Script
 * 
 * This script verifies that the build environment is properly configured
 * and all necessary components are in place for iOS/Android builds.
 */

import fs from 'fs';
import path from 'path';

const LOG_PREFIX = '[CCALC Setup Verification]';

function log(message) {
    console.log(`${LOG_PREFIX} ${message}`);
}

function error(message) {
    console.error(`${LOG_PREFIX} ❌ ${message}`);
}

function success(message) {
    console.log(`${LOG_PREFIX} ✅ ${message}`);
}

function warning(message) {
    console.warn(`${LOG_PREFIX} ⚠️ ${message}`);
}

function checkFileExists(filePath, description) {
    if (fs.existsSync(filePath)) {
        success(`${description} exists: ${filePath}`);
        return true;
    } else {
        error(`${description} missing: ${filePath}`);
        return false;
    }
}

function checkEnvironmentFile() {
    const envPath = path.join(__dirname, '.env');
    const envTemplatePath = path.join(__dirname, '.env.template');
    
    log('Checking environment configuration...');
    
    if (!checkFileExists(envPath, 'Environment file')) {
        if (checkFileExists(envTemplatePath, 'Environment template')) {
            warning('Copy .env.template to .env and configure your URLs');
            return false;
        }
    }
    
    // Check if environment variables are configured
    if (fs.existsSync(envPath)) {
        const envContent = fs.readFileSync(envPath, 'utf8');
        
        const requiredVars = [
            'EXPO_PUBLIC_BACKEND_URL',
            'EXPO_PUBLIC_FRONTEND_URL',
            'EXPO_PUBLIC_BUILD_ENV'
        ];
        
        let hasLocalhost = false;
        
        for (const variable of requiredVars) {
            if (envContent.includes(variable)) {
                success(`Environment variable ${variable} is defined`);
                
                // Check if still using localhost (might need ngrok for builds)
                const match = envContent.match(new RegExp(`${variable}=(.+)`));
                if (match && match[1].includes('localhost')) {
                    hasLocalhost = true;
                }
            } else {
                error(`Environment variable ${variable} is missing`);
            }
        }
        
        if (hasLocalhost) {
            warning('Environment uses localhost URLs - use ngrok URLs for builds');
        }
    }
    
    return true;
}

function checkBuildConfiguration() {
    log('Checking build configuration...');
    
    const configFiles = [
        { path: path.join(__dirname, 'app.config.js'), name: 'App configuration' },
        { path: path.join(__dirname, 'eas.json'), name: 'EAS build configuration' },
        { path: path.join(__dirname, 'package.json'), name: 'Package configuration' }
    ];
    
    let allFilesExist = true;
    
    for (const config of configFiles) {
        if (!checkFileExists(config.path, config.name)) {
            allFilesExist = false;
        }
    }
    
    return allFilesExist;
}

function checkPackageScripts() {
    log('Checking package.json scripts...');
    
    const packagePath = path.join(__dirname, 'package.json');
    
    if (!fs.existsSync(packagePath)) {
        error('package.json not found');
        return false;
    }
    
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    const scripts = packageJson.scripts || {};
    
    const requiredScripts = [
        'build:ios',
        'build:android',
        'build:preview',
        'build:development'
    ];
    
    let hasAllScripts = true;
    
    for (const script of requiredScripts) {
        if (scripts[script]) {
            success(`Build script '${script}' is configured`);
        } else {
            error(`Build script '${script}' is missing`);
            hasAllScripts = false;
        }
    }
    
    return hasAllScripts;
}

function checkGitHubActions() {
    log('Checking GitHub Actions workflows...');
    
    const workflowsDir = path.join(__dirname, '..', '.github', 'workflows');
    
    if (!fs.existsSync(workflowsDir)) {
        error('GitHub Actions workflows directory not found');
        return false;
    }
    
    const expectedWorkflows = [
        'ios-build.yml',
        'android-build.yml',
        'build-mobile-apps.yml'
    ];
    
    let hasAllWorkflows = true;
    
    for (const workflow of expectedWorkflows) {
        const workflowPath = path.join(workflowsDir, workflow);
        if (!checkFileExists(workflowPath, `GitHub Actions workflow ${workflow}`)) {
            hasAllWorkflows = false;
        }
    }
    
    return hasAllWorkflows;
}

function checkSetupScripts() {
    log('Checking setup scripts...');
    
    const setupScripts = [
        { path: path.join(__dirname, 'setup-build.sh'), name: 'Bash setup script' },
        { path: path.join(__dirname, 'setup-build.bat'), name: 'Windows setup script' }
    ];
    
    let hasAllScripts = true;
    
    for (const script of setupScripts) {
        if (!checkFileExists(script.path, script.name)) {
            hasAllScripts = false;
        }
    }
    
    return hasAllScripts;
}

function checkDocumentation() {
    log('Checking documentation...');
    
    const docs = [
        { path: path.join(__dirname, 'README.md'), name: 'README' },
        { path: path.join(__dirname, 'BUILD_GUIDE.md'), name: 'Build Guide' }
    ];
    
    let hasAllDocs = true;
    
    for (const doc of docs) {
        if (!checkFileExists(doc.path, doc.name)) {
            hasAllDocs = false;
        }
    }
    
    return hasAllDocs;
}

function main() {
    log('Starting CCALC Mobile App setup verification...\n');
    
    const checks = [
        { name: 'Environment Configuration', fn: checkEnvironmentFile },
        { name: 'Build Configuration', fn: checkBuildConfiguration },
        { name: 'Package Scripts', fn: checkPackageScripts },
        { name: 'GitHub Actions', fn: checkGitHubActions },
        { name: 'Setup Scripts', fn: checkSetupScripts },
        { name: 'Documentation', fn: checkDocumentation }
    ];
    
    let allPassed = true;
    const results = [];
    
    for (const check of checks) {
        log(`\n--- ${check.name} ---`);
        const passed = check.fn();
        results.push({ name: check.name, passed });
        if (!passed) allPassed = false;
    }
    
    log('\n=== VERIFICATION SUMMARY ===');
    
    for (const result of results) {
        if (result.passed) {
            success(`${result.name}: PASSED`);
        } else {
            error(`${result.name}: FAILED`);
        }
    }
    
    if (allPassed) {
        success('\n🎉 All checks passed! Your CCALC mobile app is ready for building.');
        log('\nNext steps:');
        log('1. Update .env with your ngrok URLs');
        log('2. Add EXPO_TOKEN to GitHub repository secrets');
        log('3. Run a test build: npm run build:preview');
        log('4. Use GitHub Actions for automated CI builds');
    } else {
        error('\n❌ Some checks failed. Please review the issues above.');
        process.exit(1);
    }
}


if (require.main === module) {
    main();
}

export { main };
