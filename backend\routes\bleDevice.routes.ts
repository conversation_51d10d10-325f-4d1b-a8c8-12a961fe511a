import { Router } from 'express';
import { body, param, query } from 'express-validator';
import { auth, requireAdmin } from '../middleware/auth';
import { validate } from '../middleware/validation';
import * as BleDeviceController from '../controllers/admin/bleDevice.controller';

const router = Router();

// Validation schemas
const registerBleDeviceSchema = [
  param('userId').isMongoId().withMessage('Valid user ID required'),
  body('deviceId').notEmpty().withMessage('Device ID is required'),
  body('deviceName').notEmpty().withMessage('Device name is required'),
  body('deviceType').optional().isIn(['earbud', 'headphone', 'speaker', 'generic']),
  body('adData').optional(),
  body('characteristics').optional().isObject(),
  body('signature').optional().isString(),
  body('signatureCharUuid').optional().isString(),
  body('services').optional().isArray(),
  body('rssi').optional().isNumeric(),
  body('registeredBy').optional().isIn(['script', 'admin', 'user']),
  body('scriptVersion').optional().isString(),
  body('manufacturer').optional().isString(),
  body('model').optional().isString(),
  body('notes').optional().isString()
];

const updateBleDeviceSchema = [
  param('deviceId').isMongoId().withMessage('Valid device ID required'),
  body('deviceName').optional().notEmpty(),
  body('deviceType').optional().isIn(['earbud', 'headphone', 'speaker', 'generic']),
  body('voiceCallEnabled').optional().isBoolean(),
  body('notes').optional().isString(),
  body('tags').optional().isArray(),
  body('manufacturer').optional().isString(),
  body('model').optional().isString(),
  body('isActive').optional().isBoolean()
];

/**
 * BLE Device Management Routes
 * All routes require admin authentication
 */

// Register new BLE device for a user
router.post(
  '/users/:userId/ble-devices',
  auth,
  requireAdmin,
  validate(registerBleDeviceSchema),
  BleDeviceController.registerBleDevice
);

// Get all BLE devices for a user
router.get(
  '/users/:userId/ble-devices',
  auth,
  requireAdmin,
  param('userId').isMongoId(),
  query('includeInactive').optional().isBoolean(),
  validate([]),
  BleDeviceController.getUserBleDevices
);

// Get detailed information about a specific BLE device
router.get(
  '/ble-devices/:deviceId',
  auth,
  requireAdmin,
  param('deviceId').isMongoId(),
  validate([]),
  BleDeviceController.getBleDeviceDetails
);

// Update BLE device settings
router.put(
  '/ble-devices/:deviceId',
  auth,
  requireAdmin,
  validate(updateBleDeviceSchema),
  BleDeviceController.updateBleDevice
);

// Delete/deactivate BLE device
router.delete(
  '/ble-devices/:deviceId',
  auth,
  requireAdmin,
  param('deviceId').isMongoId(),
  query('permanent').optional().isBoolean(),
  validate([]),
  BleDeviceController.deleteBleDevice
);

// Generate authentication challenge for BLE device
router.post(
  '/ble-devices/:deviceId/challenge',
  auth,
  requireAdmin,
  param('deviceId').isMongoId(),
  validate([]),
  BleDeviceController.generateBleChallenge
);

export default router;
