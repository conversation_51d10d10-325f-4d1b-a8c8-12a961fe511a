// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Minimal fix for Her<PERSON> require() error in Expo SDK 53
// This single line resolves the "Property 'require' doesn't exist" error
config.resolver.unstable_enablePackageExports = false;

module.exports = config;
