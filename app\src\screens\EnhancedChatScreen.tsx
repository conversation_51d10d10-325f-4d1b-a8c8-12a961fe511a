/**
 * Enhanced Chat Screen Component
 * Superuser-to-user messaging with media attachments (up to 50MB) and BLE voice calls
 */

import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActionSheetIOS,
} from 'react-native';

import * as Haptics from 'expo-haptics';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';

import { theme } from '../utils/theme';
import { AuthService } from '../services/AuthService';
import { ChatService, ChatMessage } from '../services/ChatService';
import { MediaService, MediaAttachment } from '../services/MediaService';
import { UserService, User } from '../services/UserService';
import bleService, { BLEDevice } from '../services/ble';
import VoiceService from '../services/VoiceService';
import { ChatListItem } from './ChatListScreen';

interface ChatScreenProps {
  chatUser?: ChatListItem;
  onBack?: () => void;
  onLogout: () => void;
}

export const ChatScreen: React.FC<ChatScreenProps> = ({ chatUser, onBack, onLogout }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedAttachment, setSelectedAttachment] = useState<MediaAttachment | null>(null);
  
  // Voice call states
  const [isInCall, setIsInCall] = useState(false);
  const [callStatus, setCallStatus] = useState<'idle' | 'connecting' | 'connected' | 'failed'>('idle');
  const [verifiedEarbud, setVerifiedEarbud] = useState<string | null>(null);
  
  const flatListRef = useRef<any>(null);
  const authService = AuthService.getInstance();
  const chatService = ChatService.getInstance();
  const mediaService = MediaService.getInstance();
  const userService = UserService.getInstance();
  // Remove this line as we're using the imported bleService directly
  const voiceService = VoiceService.getInstance();

  // Initialize chat and user data
  useEffect(() => {
    initializeChat();
    
    // Poll for new messages periodically
    const messagePollingInterval = setInterval(() => {
      loadMessages();
    }, 5000);

    // Check BLE earbuds status
    checkEarbudsStatus();

    return () => {
      clearInterval(messagePollingInterval);
    };
  }, []);

  const initializeChat = async () => {
    try {
      setIsLoading(true);
      await loadCurrentUser();
      await loadMessages();
      setIsConnected(true);
    } catch (error) {
      console.error('Failed to initialize chat:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadCurrentUser = async () => {
    try {
      const userResult = await userService.getCurrentUser();
      if (userResult.success && userResult.user) {
        setCurrentUser(userResult.user);
        console.log('👤 Current user loaded:', {
          username: userResult.user.username,
          displayName: userResult.user.displayName,
          isSuperuser: userResult.user.isSuperuser
        });
      }
    } catch (error) {
      console.error('Error loading current user:', error);
    }
  };

  const loadMessages = async () => {
    try {
      const result = await chatService.getMessages(50, 0);
      if (result.success && result.messages) {
        setMessages(result.messages.sort((a, b) => a.timestamp - b.timestamp));
        
        // Scroll to bottom after loading messages
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  const checkEarbudsStatus = async () => {
    try {
      const activeEarbud = bleService.getActiveEarbud();
      if (activeEarbud) {
        setVerifiedEarbud(activeEarbud.deviceId);
        console.log('🎧 Verified earbud found:', activeEarbud.name);
      }
    } catch (error) {
      console.error('Error checking earbuds status:', error);
    }
  };

  const handleMediaAttachment = useCallback(() => {
    if (Platform.OS === 'ios') {
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options: ['Cancel', 'Choose File', 'Camera', 'Photo Library'],
          cancelButtonIndex: 0,
        },
        async (buttonIndex: any) => {
          if (buttonIndex === 1) {
            await pickDocument();
          } else if (buttonIndex === 2) {
            await pickFromCamera();
          } else if (buttonIndex === 3) {
            await pickFromLibrary();
          }
        }
      );
    } else {
      pickDocument();
    }
  }, []);

  const pickFromCamera = async () => {
    try {
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Camera permission is needed to take photos.');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images', 'videos'],
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const file = result.assets[0];
        
        // Check file size (50MB limit)
        const maxSize = 50 * 1024 * 1024; // 50MB in bytes
        if (file.fileSize && file.fileSize > maxSize) {
          Alert.alert(
            'File Too Large',
            'Please capture a smaller photo.'
          );
          return;
        }

        const attachment: MediaAttachment = {
          id: Date.now().toString(),
          name: `photo_${Date.now()}.jpg`,
          type: 'image',
          uri: file.uri,
          mimeType: 'image/jpeg',
          size: file.fileSize || 0,
          isImage: true,
        };

        setSelectedAttachment(attachment);
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    } catch (error) {
      console.error('Error picking from camera:', error);
      Alert.alert('Error', 'Failed to capture photo');
    }
  };

  const pickFromLibrary = async () => {
    try {
      // Request media library permissions
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Media library permission is needed to select photos.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images', 'videos'],
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const file = result.assets[0];
        
        // Check file size (50MB limit)
        const maxSize = 50 * 1024 * 1024; // 50MB in bytes
        if (file.fileSize && file.fileSize > maxSize) {
          Alert.alert(
            'File Too Large',
            'Please select a smaller file.'
          );
          return;
        }

        const attachment: MediaAttachment = {
          id: Date.now().toString(),
          name: file.fileName || `media_${Date.now()}.jpg`,
          type: file.type?.startsWith('image/') ? 'image' : 'media',
          uri: file.uri,
          mimeType: file.type || 'image/jpeg',
          size: file.fileSize || 0,
          isImage: file.type?.startsWith('image/') || true,
        };

        setSelectedAttachment(attachment);
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    } catch (error) {
      console.error('Error picking from library:', error);
      Alert.alert('Error', 'Failed to select media');
    }
  };

  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
        multiple: false,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const file = result.assets[0];
        
        // Check file size (50MB limit)
        const maxSize = 50 * 1024 * 1024; // 50MB in bytes
        if (file.size && file.size > maxSize) {
          Alert.alert(
            'File Too Large',
            'Please select a file smaller than 50MB.'
          );
          return;
        }

        const attachment: MediaAttachment = {
          id: Date.now().toString(),
          name: file.name,
          uri: file.uri,
          mimeType: file.mimeType || 'application/octet-stream',
          size: file.size || 0,
          type: 'file',
        };

        setSelectedAttachment(attachment);
        console.log('📎 File selected:', attachment.name, `${(attachment.size / 1024 / 1024).toFixed(2)}MB`);
      }
    } catch (error) {
      console.error('Document picker error:', error);
      Alert.alert('Error', 'Failed to select file');
    }
  };

  const clearAttachment = () => {
    setSelectedAttachment(null);
  };

  const sendMessage = useCallback(async () => {
    if ((!inputText.trim() && !selectedAttachment) || !isConnected) return;

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    const messageId = Date.now().toString();
    const newMessage: ChatMessage = {
      id: messageId,
      chatId: chatUser?.id || 'default-chat',
      text: inputText.trim() || (selectedAttachment ? `📎 ${selectedAttachment.name}` : ''),
      sender: currentUser?.isSuperuser ? 'superuser' : 'user',
      timestamp: Date.now(),
      status: 'sent',
      isDelivered: false,
      isRead: false,
      attachment: selectedAttachment || undefined,
    };

    // Add message to local state immediately
    setMessages(prev => [...prev, newMessage]);
    setInputText('');
    const attachmentToSend = selectedAttachment;
    setSelectedAttachment(null);

    // Scroll to bottom
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);

    try {
      const result = await chatService.sendMessage(newMessage.text, attachmentToSend || undefined);

      if (result.success) {
        // Update message status
        setMessages(prev => prev.map(msg => 
          msg.id === messageId 
            ? { ...msg, status: 'delivered', isDelivered: true }
            : msg
        ));
      } else {
        // Mark message as failed
        setMessages(prev => prev.map(msg => 
          msg.id === messageId 
            ? { ...msg, status: 'failed' }
            : msg
        ));
        
        Alert.alert('Send Failed', result.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('Send message error:', error);
      
      // Mark message as failed
      setMessages(prev => prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, status: 'failed' }
          : msg
      ));
      
      Alert.alert('Error', 'Failed to send message');
    }
  }, [inputText, selectedAttachment, isConnected, chatUser?.id, currentUser, chatService]);

  const initiateVoiceCall = useCallback(async () => {
    try {
      // Check if verified earbud is available
      if (!verifiedEarbud) {
        Alert.alert(
          'No Verified Earbuds',
          'Please connect and verify your earbuds before initiating a voice call.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Scan Earbuds', onPress: scanForEarbuds },
          ]
        );
        return;
      }

      // Check if earbud can initiate call
      if (!bleService.getActiveEarbud()) {
        Alert.alert('Voice Call Error', 'Verified earbuds are not connected or active.');
        return;
      }

      setCallStatus('connecting');
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);

      // Start voice call with verified earbud
      try {
        const callResult = await voiceService.startVoiceCall(
          chatUser?.id || 'unknown',
          currentUser?.isSuperuser ? 'superuser' : 'agent'
        );

        setIsInCall(true);
        setCallStatus('connected');
        console.log('📞 Voice call initiated successfully');

        // Add system message
        const callMessage: ChatMessage = {
          id: Date.now().toString(),
          chatId: chatUser?.id || 'default-chat',
          text: '📞 Voice call started (Earbud-only mode)',
          sender: currentUser?.isSuperuser ? 'superuser' : 'user',
          timestamp: Date.now(),
          status: 'delivered',
          isDelivered: true,
          isRead: true,
        };
        setMessages(prev => [...prev, callMessage]);
      } catch (error) {
        setCallStatus('failed');
        setTimeout(() => setCallStatus('idle'), 3000);
        Alert.alert('Call Failed', 'Failed to initiate voice call');
      }
    } catch (error) {
      console.error('Voice call error:', error);
      setCallStatus('failed');
      setTimeout(() => setCallStatus('idle'), 3000);
      Alert.alert('Call Error', 'Failed to initiate voice call');
    }
  }, [verifiedEarbud, chatUser?.id, bleService, voiceService]);

  const endVoiceCall = useCallback(async () => {
    try {
      setCallStatus('idle');
      setIsInCall(false);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      await voiceService.endVoiceCall();

      // Add system message
      const callEndMessage: ChatMessage = {
        id: Date.now().toString(),
        chatId: chatUser?.id || 'default-chat',
        text: '📞 Voice call ended',
        sender: currentUser?.isSuperuser ? 'superuser' : 'user',
        timestamp: Date.now(),
        status: 'delivered',
        isDelivered: true,
        isRead: true,
      };
      setMessages(prev => [...prev, callEndMessage]);
    } catch (error) {
      console.error('End call error:', error);
    }
  }, [chatUser?.id, voiceService]);

  const scanForEarbuds = useCallback(async () => {
    try {
      Alert.alert(
        'Scanning for Earbuds',
        'Scanning for BLE earbuds...',
        [{ text: 'Cancel' }]
      );

      const devices = await bleService.scanForDevices(10000);
      
      if (devices.length === 0) {
        Alert.alert('No Earbuds Found', 'No compatible earbuds were found. Please ensure they are in pairing mode.');
        return;
      }

      // Show device selection
      const deviceNames = devices.map((d: BLEDevice) => d.name);
      const options = ['Cancel', ...deviceNames];

      if (Platform.OS === 'ios') {
        ActionSheetIOS.showActionSheetWithOptions(
          {
            options,
            cancelButtonIndex: 0,
            title: 'Select Earbuds to Connect',
          },
          async (buttonIndex: any) => {
            if (buttonIndex > 0) {
              const selectedDevice = devices[buttonIndex - 1];
              await connectAndVerifyEarbud(selectedDevice.deviceId);
            }
          }
        );
      }
    } catch (error) {
      console.error('Earbud scan error:', error);
      Alert.alert('Scan Error', 'Failed to scan for earbuds');
    }
  }, [bleService]);

  const connectAndVerifyEarbud = async (deviceId: string) => {
    try {
      Alert.alert('Connecting', 'Connecting to earbuds...');

      // Verify device for voice calls (similar to Python script)
      const verificationResult = await bleService.verifyEarbud(deviceId);
      
      if (verificationResult) {
        setVerifiedEarbud(deviceId);
        
        Alert.alert(
          'Earbuds Verified',
          `Earbuds have been verified and are ready for voice calls.`
        );
        
        console.log('✅ Earbud verified and set as active');
      } else {
        Alert.alert(
          'Verification Failed',
          'Failed to verify earbuds for voice calls'
        );
      }
    } catch (error) {
      console.error('Earbud connection error:', error);
      Alert.alert('Connection Error', 'Failed to connect and verify earbuds');
    }
  };

  const formatMessageTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderMessage = ({ item }: { item: ChatMessage }) => {
    const isOwnMessage = item.sender === (currentUser?.isSuperuser ? 'superuser' : 'user');
    // Remove system message check as our ChatMessage type doesn't support it
    // const isSystemMessage = item.sender === 'system';
    const isSystemMessage = false;

    return (
      <View style={[
        styles.messageContainer,
        isOwnMessage && styles.ownMessageContainer,
        isSystemMessage && styles.systemMessageContainer,
      ]}>
        <View style={[
          styles.messageBubble,
          isOwnMessage && styles.ownMessageBubble,
          isSystemMessage && styles.systemMessageBubble,
        ]}>
          {item.attachment && (
            <View style={styles.attachmentContainer}>
              <Text style={styles.attachmentIcon}>📎</Text>
              <Text style={styles.attachmentName}>{item.attachment.name}</Text>
              <Text style={styles.attachmentSize}>
                {((item.attachment.size || 0) / 1024 / 1024).toFixed(2)}MB
              </Text>
            </View>
          )}
          
          <Text style={[
            styles.messageText,
            isOwnMessage && styles.ownMessageText,
            isSystemMessage && styles.systemMessageText,
          ]}>
            {item.text}
          </Text>
          
          <View style={styles.messageFooter}>
            <Text style={[
              styles.messageTime,
              isOwnMessage && styles.ownMessageTime,
            ]}>
              {formatMessageTime(item.timestamp)}
            </Text>
            
            {isOwnMessage && (
              <Text style={styles.messageStatus}>
                {item.status === 'sent' && '↗'}
                {item.status === 'delivered' && '✓'}
                {item.status === 'read' && '✓✓'}
                {item.status === 'failed' && '⚠'}
              </Text>
            )}
          </View>
        </View>
      </View>
    );
  };

  const renderInputArea = () => (
    <View style={styles.inputContainer}>
      {selectedAttachment && (
        <View style={styles.selectedAttachmentContainer}>
          <View style={styles.selectedAttachment}>
            <Text style={styles.attachmentIcon}>📎</Text>
            <View style={styles.attachmentInfo}>
              <Text style={styles.attachmentName}>{selectedAttachment.name}</Text>
              <Text style={styles.attachmentSize}>
                {((selectedAttachment.size || 0) / 1024 / 1024).toFixed(2)}MB
              </Text>
            </View>
            <TouchableOpacity onPress={clearAttachment} style={styles.clearButton}>
              <Text style={styles.clearButtonText}>✕</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
      
      <View style={styles.inputRow}>
        <TouchableOpacity 
          style={styles.attachButton}
          onPress={handleMediaAttachment}
        >
          <Text style={styles.attachButtonText}>📎</Text>
        </TouchableOpacity>
        
        <TextInput
          style={styles.textInput}
          value={inputText}
          onChangeText={setInputText}
          placeholder="Type a message..."
          placeholderTextColor={theme.colors.secondaryLabel}
          multiline
          maxLength={1000}
        />
        
        <TouchableOpacity 
          style={[styles.voiceButton, verifiedEarbud && styles.voiceButtonActive]}
          onPress={isInCall ? endVoiceCall : initiateVoiceCall}
          disabled={callStatus === 'connecting'}
        >
          <Text style={styles.voiceButtonText}>
            {isInCall ? '📞❌' : 
             callStatus === 'connecting' ? '🔄' : 
             verifiedEarbud ? '📞🎧' : '📞⚠'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[
            styles.sendButton,
            (!inputText.trim() && !selectedAttachment) && styles.sendButtonDisabled
          ]}
          onPress={sendMessage}
          disabled={!inputText.trim() && !selectedAttachment}
        >
          <Text style={styles.sendButtonText}>Send</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>
            {chatUser?.displayName || (currentUser?.isSuperuser ? 'Select User' : 'user')}
          </Text>
          <Text style={styles.headerSubtitle}>
            {isInCall ? '📞 Voice Call Active' :
             callStatus === 'connecting' ? '🔄 Connecting...' :
             verifiedEarbud ? '🎧 Ready for calls' :
             isConnected ? 'Online' : 'Connecting...'}
          </Text>
        </View>
        
        <TouchableOpacity style={styles.menuButton} onPress={onLogout}>
          <Text style={styles.menuButtonText}>⋯</Text>
        </TouchableOpacity>
      </View>

      {/* Messages */}
      <KeyboardAvoidingView 
        style={styles.chatContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={90}
      >
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item: any) => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
          onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
        />
        
        {renderInputArea()}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.systemGray5,
    backgroundColor: theme.colors.secondaryBackground,
  },
  backButton: {
    padding: theme.spacing.xs,
  },
  backButtonText: {
    fontSize: 16,
    color: theme.colors.systemBlue,
    fontWeight: '600',
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: theme.colors.label,
  },
  headerSubtitle: {
    fontSize: 13,
    color: theme.colors.secondaryLabel,
    marginTop: 2,
  },
  menuButton: {
    padding: theme.spacing.xs,
  },
  menuButtonText: {
    fontSize: 20,
    color: theme.colors.systemBlue,
  },
  chatContainer: {
    flex: 1,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: theme.spacing.md,
  },
  messageContainer: {
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.sm,
  },
  ownMessageContainer: {
    alignItems: 'flex-end',
  },
  systemMessageContainer: {
    alignItems: 'center',
  },
  messageBubble: {
    backgroundColor: theme.colors.systemGray5,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: 18,
    maxWidth: '80%',
  },
  ownMessageBubble: {
    backgroundColor: theme.colors.systemBlue,
  },
  systemMessageBubble: {
    backgroundColor: theme.colors.systemGray4,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: 12,
  },
  messageText: {
    fontSize: 16,
    color: theme.colors.label,
    lineHeight: 20,
  },
  ownMessageText: {
    color: '#FFFFFF',
  },
  systemMessageText: {
    fontSize: 14,
    color: theme.colors.secondaryLabel,
    fontStyle: 'italic',
  },
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: theme.spacing.xs,
  },
  messageTime: {
    fontSize: 12,
    color: theme.colors.secondaryLabel,
  },
  ownMessageTime: {
    color: '#FFFFFF80',
  },
  messageStatus: {
    fontSize: 12,
    color: '#FFFFFF80',
    marginLeft: theme.spacing.xs,
  },
  attachmentContainer: {
    backgroundColor: theme.colors.systemGray6,
    padding: theme.spacing.sm,
    borderRadius: 8,
    marginBottom: theme.spacing.xs,
  },
  attachmentIcon: {
    fontSize: 16,
  },
  attachmentName: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.label,
  },
  attachmentSize: {
    fontSize: 12,
    color: theme.colors.secondaryLabel,
  },
  inputContainer: {
    backgroundColor: theme.colors.secondaryBackground,
    borderTopWidth: 1,
    borderTopColor: theme.colors.systemGray5,
  },
  selectedAttachmentContainer: {
    padding: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.systemGray5,
  },
  selectedAttachment: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.systemGray5,
    padding: theme.spacing.sm,
    borderRadius: 8,
  },
  attachmentInfo: {
    flex: 1,
    marginLeft: theme.spacing.sm,
  },
  clearButton: {
    padding: theme.spacing.xs,
    backgroundColor: theme.colors.systemRed,
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  clearButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
  },
  attachButton: {
    padding: theme.spacing.sm,
    marginRight: theme.spacing.xs,
  },
  attachButtonText: {
    fontSize: 20,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: theme.colors.systemGray5,
    borderRadius: 20,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    fontSize: 16,
    color: theme.colors.label,
    backgroundColor: theme.colors.background,
    maxHeight: 100,
  },
  voiceButton: {
    padding: theme.spacing.sm,
    marginLeft: theme.spacing.xs,
    backgroundColor: theme.colors.systemGray5,
    borderRadius: 20,
  },
  voiceButtonActive: {
    backgroundColor: theme.colors.systemGreen,
  },
  voiceButtonText: {
    fontSize: 16,
  },
  sendButton: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.systemBlue,
    borderRadius: 20,
    marginLeft: theme.spacing.xs,
  },
  sendButtonDisabled: {
    backgroundColor: theme.colors.systemGray4,
  },
  sendButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ChatScreen;
