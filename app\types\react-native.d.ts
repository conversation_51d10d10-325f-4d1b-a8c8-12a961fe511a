// Override React Native component types to fix compatibility with React 18.3.x
declare module "react-native" {
  import { ComponentType } from "react";
  
  // Override all React Native component types to be compatible with React 18
  export const View: ComponentType<any>;
  export const Text: ComponentType<any>;
  export const TouchableOpacity: ComponentType<any>;
  export const SafeAreaView: ComponentType<any>;
  export const ScrollView: ComponentType<any>;
  export const FlatList: ComponentType<any>;
  export const TextInput: ComponentType<any>;
  export const Modal: ComponentType<any>;
  export const StatusBar: ComponentType<any>;
  export const KeyboardAvoidingView: ComponentType<any>;
  export const Image: ComponentType<any>;
  export const Pressable: ComponentType<any>;
  export const ActivityIndicator: ComponentType<any>;
  export const RefreshControl: ComponentType<any>;
  export const Vibration: any;
  export const ActionSheetIOS: any;
  export const NativeModules: any;
  export const NativeEventEmitter: any;
  export const Alert: any;
  export const Platform: any;
  export const Dimensions: any;
  export const StyleSheet: any;
  export const Animated: any;
  export const Easing: any;
  export const AppState: any;
  export const Linking: any;
  export const BackHandler: any;
}

declare module "react-native-gesture-handler" {
  import { ComponentType } from "react";
  export const PanGestureHandler: ComponentType<any>;
  export const GestureHandlerRootView: ComponentType<any>;
  export const Swipeable: ComponentType<any>;
  export const State: any;
}

declare module "expo-av" {
  import { ComponentType } from "react";
  export const Video: ComponentType<any>;
  export const Audio: any;
  export const ResizeMode: any;
}

// Fix NodeJS Timeout type
declare global {
  namespace NodeJS {
    interface Timeout {}
  }
}
