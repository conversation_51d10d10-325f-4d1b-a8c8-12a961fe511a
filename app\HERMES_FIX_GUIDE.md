# Hermes Compatibility Fix for Expo SDK 53

## Problem Description
The error `[runtime not ready]: ReferenceError: Property 'require' doesn't exist, js engine: hermes` occurs when using Expo SDK 53 with Hermes JavaScript engine. This is a known compatibility issue where Metro bundler or certain dependencies inject CommonJS `require()` calls that <PERSON><PERSON> cannot handle at runtime.

## Root Causes Identified
1. **crypto-js dependency**: Uses CommonJS patterns incompatible with Hermes
2. **Metro configuration**: Default settings can inject require() calls
3. **Babel configuration**: Certain plugins can generate problematic code
4. **react-native-webrtc**: May have compatibility issues with <PERSON><PERSON> in SDK 53

## Fixes Applied

### 1. Metro Configuration (`metro.config.js`)
- **Disabled inline requires**: `inlineRequires: false` to prevent runtime require() calls
- **Disabled package exports**: `unstable_enablePackageExports: false`
- **Removed 'require' from condition names**: Prevents CommonJS injection
- **Added crypto-js alias**: Forces specific module resolution
- **Configured resolver main fields**: Prioritizes React Native compatible exports

### 2. Babel Configuration (`babel.config.js`)
- **Simplified preset configuration**: Removed problematic options
- **Removed custom require transformer**: The custom plugin was causing issues
- **Kept module resolver**: For path aliases only

### 3. App Configuration (`app.config.js`)
- **Disabled experimental features**: `turboModules: false`, `newArchEnabled: false`
- **Kept Hermes enabled**: `jsEngine: "hermes"`
- **Disabled updates**: Prevents update-related require() issues

### 4. Encryption Utilities
- **Created Hermes-compatible fallback**: `encryption-hermes.ts`
- **Updated main encryption**: Falls back to Expo.Crypto when crypto-js fails
- **Graceful degradation**: App continues working even if crypto-js fails

## Files Modified
- `app/metro.config.js` - Complete Metro configuration overhaul
- `app/babel.config.js` - Simplified Babel configuration
- `app/app.config.js` - Added Hermes compatibility settings
- `app/src/utils/encryption.ts` - Added fallback for crypto-js
- `app/src/utils/encryption-hermes.ts` - New Hermes-compatible encryption

## Files Removed
- `app/babel-plugin-hermes-require-transformer.js` - Problematic custom plugin

## Testing Steps

### 1. Clear Cache and Restart
```bash
cd app
npx expo start --clear --port 8082
```

### 2. Test Configuration
```bash
node test-hermes-fix.js
```

### 3. Test on Device
1. Open Expo Go on iOS device
2. Scan QR code or enter URL
3. Check for require() errors in console
4. Test app functionality

### 4. Verify Encryption
- Test chat message encryption/decryption
- Verify fallback encryption works
- Check console for crypto-js warnings

## Expected Behavior After Fix
- ✅ App loads without require() errors
- ✅ Encryption works (with fallback if needed)
- ✅ WebRTC functionality preserved
- ✅ All Expo features work normally

## Troubleshooting

### If Error Persists
1. **Check dependencies**: Some packages may still use require()
2. **Update packages**: Ensure all packages are SDK 53 compatible
3. **Check imports**: Look for dynamic imports or require() calls in code
4. **Metro cache**: Clear with `--reset-cache` flag

### Alternative Solutions
1. **Disable Hermes**: Set `jsEngine: "jsc"` (not recommended)
2. **Use development build**: Create custom development build
3. **Replace problematic packages**: Find Hermes-compatible alternatives

### Debug Commands
```bash
# Clear all caches
npx expo start --clear --reset-cache

# Check bundle for require() calls
npx expo export --platform ios --dev

# Verbose logging
npx expo start --port 8082 --verbose
```

## Known Limitations
1. **crypto-js fallback**: Uses simpler XOR encryption instead of AES
2. **Performance**: Fallback encryption may be slower
3. **Compatibility**: Some advanced crypto features not available in fallback

## Production Considerations
1. **Security**: Implement proper AES encryption for production
2. **Testing**: Thoroughly test all encryption/decryption scenarios
3. **Monitoring**: Add error tracking for crypto failures
4. **Fallback strategy**: Ensure graceful degradation

## Additional Resources
- [Expo SDK 53 Release Notes](https://expo.dev/changelog/2025/05-01-sdk-53)
- [Hermes JavaScript Engine](https://hermesengine.dev/)
- [Metro Bundler Configuration](https://metrobundler.dev/docs/configuration)
- [React Native Hermes Guide](https://reactnative.dev/docs/hermes)

## Support
If issues persist after applying these fixes:
1. Check Expo forums for similar issues
2. Review Metro bundler logs for specific require() calls
3. Consider creating a minimal reproduction case
4. Update to latest Expo SDK patch version
