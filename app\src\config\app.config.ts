/**
 * CCALC App Configuration
 * Development configuration for connecting iPhone to local backend
 */

// Get your local machine's IP address
// Your Windows machine IP addresses: *************, *************, *************, ************
// Use the one that matches your WiFi network (usually 192.168.x.x)

const LOCAL_IP = '*************'; // ✅ Set to your most likely network IP

export const AppConfig = {
  // Backend connection settings
  backend: {
    // For iPhone testing, use your computer's local IP address
    // Not localhost/127.0.0.1 because that would point to the iPhone itself
    baseUrl: __DEV__
      ? `http://${LOCAL_IP}:3000`
      : 'https://your-production-backend.com',

    timeout: 60000, // 60 seconds for voice processing
  },

  // API endpoints
  endpoints: {
    auth: '/api/auth',
    chat: '/api/chat',
    calls: '/api/calls',
    voice: '/api/voice',
    device: '/api/device',
    health: '/api/health',
  },

  // App settings
  app: {
    name: 'CCAL<PERSON>',
    version: '1.0.0',
    environment: __DEV__ ? 'development' : 'production',
  },

  // Security settings
  security: {
    encryptionKeySize: 256,
    sessionTimeout: 30 * 60 * 1000, // 30 minutes
    maxFailedAttempts: 3,
  },

  // Voice settings
  voice: {
    maxRecordingDuration: 300, // 5 minutes
    audioFormat: 'mp4',
    sampleRate: 44100,
  },

  // BLE settings
  ble: {
    scanDuration: 10000, // 10 seconds
    connectionTimeout: 5000, // 5 seconds
  },
};

// Validation function
export const validateConfig = (): boolean => {
  if (!LOCAL_IP || LOCAL_IP.includes('YOUR_LOCAL_IP')) {
    console.error('❌ Please update LOCAL_IP in app.config.ts with your actual IP address');
    return false;
  }

  console.log('✅ App configuration validated');
  console.log(`📡 Backend URL: ${AppConfig.backend.baseUrl}`);
  return true;
};

export default AppConfig;
