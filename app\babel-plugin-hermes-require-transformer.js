// Custom Babel plugin to transform require() calls to imports for Hermes compatibility
module.exports = function() {
  return {
    name: "hermes-require-transformer",
    visitor: {
      CallExpression(path) {
        // Transform require() calls to throw an error indicating the issue
        if (
          path.node.callee.type === 'Identifier' &&
          path.node.callee.name === 'require' &&
          path.node.arguments.length === 1 &&
          path.node.arguments[0].type === 'StringLiteral'
        ) {
          // Replace require() with a descriptive error for debugging
          const moduleName = path.node.arguments[0].value;
          path.replaceWithSourceString(
            `(() => { throw new Error("Hermes compatibility error: require('${moduleName}') should be replaced with import statement"); })()`
          );
        }
      },
      
      // Transform _interopRequireDefault calls that might be added by other babel plugins
      VariableDeclarator(path) {
        if (
          path.node.id.type === 'Identifier' &&
          path.node.id.name.includes('interopRequireDefault') &&
          path.node.init &&
          path.node.init.type === 'CallExpression'
        ) {
          // Remove problematic interop require calls
          path.remove();
        }
      }
    }
  };
};
