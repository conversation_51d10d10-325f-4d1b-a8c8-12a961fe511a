import { Request, Response } from 'express';
import mongoose from 'mongoose';
import BleDevice from '../../models/BleDevice';
import User from '../../models/User';
import { createHash } from 'crypto';

/**
 * BLE Device Management Controller for Admin Panel
 * Handles comprehensive BLE device registration, verification, and management
 */

/**
 * Register a new BLE device for a user (from Python script or admin panel)
 * POST /api/admin/users/:userId/ble-devices
 */
export async function registerBleDevice(req: Request, res: Response): Promise<void> {
  try {
    const { userId } = req.params;
    const {
      deviceId,
      deviceName,
      deviceType = 'generic',
      adData,
      characteristics = {},
      signature,
      signatureCharUuid,
      services = [],
      rssi,
      registeredBy = 'script',
      scriptVersion,
      manufacturer,
      model,
      notes
    } = req.body;

    // Validate required fields
    if (!deviceId || !deviceName) {
      res.status(400).json({
        success: false,
        error: 'Missing required fields: deviceId, deviceName'
      });
      return;
    }

    // Verify user exists
    const user = await User.findById(userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Check if device already exists for this user
    const existingDevice = await BleDevice.findOne({ userId, deviceId });
    if (existingDevice) {
      res.status(409).json({
        success: false,
        error: 'BLE device already registered for this user',
        device: existingDevice
      });
      return;
    }

    // Create new BLE device record
    const bleDevice = new BleDevice({
      userId,
      deviceId,
      deviceName,
      deviceType,
      registrationData: {
        adData,
        characteristics,
        signature,
        signatureCharUuid,
        services,
        rssi,
        registeredAt: new Date(),
        registeredBy,
        scriptVersion,
        registrationIP: req.ip
      },
      status: {
        isVerified: !!signature, // Verified if signature was written
        isActive: true,
        lastSeen: new Date(),
        connectionCount: 0
      },
      voiceCallAuth: {
        enabled: true,
        callCount: 0,
        authFailures: 0,
        authSuccessRate: 100
      },
      security: {
        riskScore: 0,
        violations: [],
        securityFlags: []
      },
      metadata: {
        manufacturer,
        model,
        notes,
        addedBy: req.user?.id,
        lastModified: new Date()
      },
      performance: {
        averageRssi: rssi,
        connectionReliability: 100,
        audioDropouts: 0
      },
      integration: {
        syncedWithUserModel: false
      }
    });

    await bleDevice.save();

    // Also add to User model for backward compatibility
    const deviceEntry = user.devices.find(d => d.deviceId === deviceId) || {
      deviceId,
      fingerprint: createHash('sha256').update(deviceId).digest('hex'),
      deviceType: 'mobile',
      deviceModel: model || '',
      os: '',
      browser: '',
      lastUsed: new Date(),
      isActive: true,
      bleDevices: []
    };

    // Add BLE device to user's device entry
    deviceEntry.bleDevices.push({
      deviceId,
      deviceName,
      pairedAt: new Date(),
      lastConnected: new Date(),
      isVerified: !!signature,
      adData,
      characteristics,
      signature,
      signatureCharUuid
    });

    // Add device entry if it's new
    if (!user.devices.find(d => d.deviceId === deviceId)) {
      user.devices.push(deviceEntry);
    }

    await user.save();

    // Mark as synced
    bleDevice.integration.syncedWithUserModel = true;
    bleDevice.integration.lastSyncAt = new Date();
    await bleDevice.save();

    res.status(201).json({
      success: true,
      message: 'BLE device registered successfully',
      device: bleDevice
    });

  } catch (error) {
    console.error('Error registering BLE device:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to register BLE device'
    });
  }
}

/**
 * Get all BLE devices for a user
 * GET /api/admin/users/:userId/ble-devices
 */
export async function getUserBleDevices(req: Request, res: Response): Promise<void> {
  try {
    const { userId } = req.params;
    const { includeInactive = false } = req.query;

    const query: any = { userId };
    if (!includeInactive) {
      query['status.isActive'] = true;
    }

    const devices = await BleDevice.find(query)
      .sort({ 'registrationData.registeredAt': -1 });

    res.json({
      success: true,
      devices,
      count: devices.length
    });

  } catch (error) {
    console.error('Error fetching user BLE devices:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch BLE devices'
    });
  }
}

/**
 * Get detailed information about a specific BLE device
 * GET /api/admin/ble-devices/:deviceId
 */
export async function getBleDeviceDetails(req: Request, res: Response): Promise<void> {
  try {
    const { deviceId } = req.params;

    const device = await BleDevice.findById(deviceId)
      .populate('userId', 'username profile.displayName')
      .populate('metadata.addedBy', 'username')
      .populate('metadata.lastModifiedBy', 'username');

    if (!device) {
      res.status(404).json({
        success: false,
        error: 'BLE device not found'
      });
      return;
    }

    res.json({
      success: true,
      device
    });

  } catch (error) {
    console.error('Error fetching BLE device details:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch BLE device details'
    });
  }
}

/**
 * Update BLE device settings
 * PUT /api/admin/ble-devices/:deviceId
 */
export async function updateBleDevice(req: Request, res: Response): Promise<void> {
  try {
    const { deviceId } = req.params;
    const {
      deviceName,
      deviceType,
      voiceCallEnabled,
      notes,
      tags,
      manufacturer,
      model,
      isActive
    } = req.body;

    const device = await BleDevice.findById(deviceId);
    if (!device) {
      res.status(404).json({
        success: false,
        error: 'BLE device not found'
      });
      return;
    }

    // Update fields
    if (deviceName) device.deviceName = deviceName;
    if (deviceType) device.deviceType = deviceType;
    if (typeof voiceCallEnabled === 'boolean') {
      device.voiceCallAuth.enabled = voiceCallEnabled;
    }
    if (notes !== undefined) device.metadata.notes = notes;
    if (tags) device.metadata.tags = tags;
    if (manufacturer) device.metadata.manufacturer = manufacturer;
    if (model) device.metadata.model = model;
    if (typeof isActive === 'boolean') device.status.isActive = isActive;

    device.metadata.lastModifiedBy = req.user?.id;
    device.metadata.lastModified = new Date();

    await device.save();

    res.json({
      success: true,
      message: 'BLE device updated successfully',
      device
    });

  } catch (error) {
    console.error('Error updating BLE device:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update BLE device'
    });
  }
}

/**
 * Delete/deactivate a BLE device
 * DELETE /api/admin/ble-devices/:deviceId
 */
export async function deleteBleDevice(req: Request, res: Response): Promise<void> {
  try {
    const { deviceId } = req.params;
    const { permanent = false } = req.query;

    const device = await BleDevice.findById(deviceId);
    if (!device) {
      res.status(404).json({
        success: false,
        error: 'BLE device not found'
      });
      return;
    }

    if (permanent === 'true') {
      // Permanently delete the device
      await BleDevice.findByIdAndDelete(deviceId);
      
      // Also remove from User model
      await User.updateOne(
        { _id: device.userId },
        { 
          $pull: { 
            'devices.$[].bleDevices': { deviceId: device.deviceId },
            bleDevices: { deviceId: device.deviceId }
          }
        }
      );

      res.json({
        success: true,
        message: 'BLE device permanently deleted'
      });
    } else {
      // Just deactivate
      device.status.isActive = false;
      device.metadata.lastModified = new Date();
      device.metadata.lastModifiedBy = req.user?.id;
      await device.save();

      res.json({
        success: true,
        message: 'BLE device deactivated'
      });
    }

  } catch (error) {
    console.error('Error deleting BLE device:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete BLE device'
    });
  }
}

/**
 * Generate authentication challenge for BLE device verification
 * POST /api/admin/ble-devices/:deviceId/challenge
 */
export async function generateBleChallenge(req: Request, res: Response): Promise<void> {
  try {
    const { deviceId } = req.params;

    const device = await BleDevice.findById(deviceId);
    if (!device) {
      res.status(404).json({
        success: false,
        error: 'BLE device not found'
      });
      return;
    }

    // Generate new challenge
    const challenge = createHash('sha256')
      .update(`${device.deviceId}-${Date.now()}-${Math.random()}`)
      .digest('hex');

    device.security.authChallenge = challenge;
    device.security.challengeExpiry = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
    await device.save();

    res.json({
      success: true,
      challenge,
      expiresAt: device.security.challengeExpiry
    });

  } catch (error) {
    console.error('Error generating BLE challenge:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate challenge'
    });
  }
}
