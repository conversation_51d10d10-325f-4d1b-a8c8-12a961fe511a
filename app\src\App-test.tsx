/**
 * Test App Component - Minimal version to test Hermes compatibility
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export default function TestApp() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>CCALC Test</Text>
      <Text style={styles.subtitle}>Hermes Compatibility Test</Text>
      <Text style={styles.status}>✅ React 18.2.0 Loaded</Text>
      <Text style={styles.status}>✅ Hermes Engine Active</Text>
      <Text style={styles.status}>✅ Metro Bundler Working</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 18,
    color: '#666',
    marginBottom: 20,
  },
  status: {
    fontSize: 16,
    color: '#007AFF',
    marginBottom: 10,
    textAlign: 'center',
  },
});
